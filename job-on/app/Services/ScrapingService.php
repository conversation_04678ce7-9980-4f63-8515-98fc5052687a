<?php

namespace App\Services;

use App\Models\Business;
use App\Models\ScrapingJob;
use App\Services\JobNotificationService;
use App\Services\BusinessDataCompletenessService;
use App\Jobs\EnhanceBusinessDataJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

class ScrapingService
{
    protected $jobNotificationService;
    protected $completenessService;

    public function __construct(JobNotificationService $jobNotificationService, BusinessDataCompletenessService $completenessService)
    {
        $this->jobNotificationService = $jobNotificationService;
        $this->completenessService = $completenessService;
    }

    /**
     * Scrape businesses for a given scraping job.
     *
     * @param ScrapingJob $scrapingJob
     * @return void
     */
    public function scrape(ScrapingJob $scrapingJob): void
    {
        Log::info('Starting to scrape businesses for scraping job', ['scraping_job_id' => $scrapingJob->id]);

        // Check if this is for enhancing existing businesses or finding new ones
        $campaign = $scrapingJob->jobNotificationCampaign;
        $existingBusinesses = $this->findExistingBusinessesInArea($campaign->job_zip_code, $campaign->job_category);

        if ($existingBusinesses->isNotEmpty()) {
            Log::info('Found existing businesses in area, checking for data completeness', [
                'scraping_job_id' => $scrapingJob->id,
                'existing_businesses_count' => $existingBusinesses->count(),
            ]);

            // Enhance existing businesses with incomplete data
            $this->enhanceExistingBusinesses($scrapingJob, $existingBusinesses);
        } else {
            Log::info('No existing businesses found, scraping new businesses', [
                'scraping_job_id' => $scrapingJob->id,
            ]);

            // For now, we will simulate finding a few businesses.
            $this->simulateScraping($scrapingJob);
        }

        // Once scraping is done and businesses are added, we need to
        // re-trigger the notification process for the original campaign.
        $this->processCampaignAfterScraping($scrapingJob);
    }

    /**
     * Simulate scraping and creating businesses.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function simulateScraping(ScrapingJob $scrapingJob): void
    {
        $locationQuery = $scrapingJob->location_query;
        $categoryQuery = $scrapingJob->category_query;

        // Simulate finding 2 businesses using the same structure as ImportBusinessesJob
        $discoveredCount = 0;

        for ($i = 1; $i <= 2; $i++) {
            try {
                $reviews = [
                    [
                        'text' => 'Great service from scraped business!', // Use 'text' instead of 'comment'
                        'rating' => 5,
                        'author' => 'Satisfied Customer',
                        'date' => date('Y-m-d')
                    ]
                ];
                $rating = array_sum(array_column($reviews, 'rating')) / count($reviews);
                Business::create([
                    'name' => "Scraped Business $i from $locationQuery",
                    'address' => "$locationQuery, Sample Address $i",
                    'phone' => "******-000-000$i",
                    'website' => "https://scraped-business-$i.com",
                    'category' => $categoryQuery,
                    'location' => "$locationQuery",
                    'email' => "business$<EMAIL>",
                    'hours' => [
                        'monday' => ['open' => '09:00', 'close' => '17:00'],
                        'tuesday' => ['open' => '09:00', 'close' => '17:00'],
                        'wednesday' => ['open' => '09:00', 'close' => '17:00'],
                        'thursday' => ['open' => '09:00', 'close' => '17:00'],
                        'friday' => ['open' => '09:00', 'close' => '17:00'],
                        'saturday' => ['open' => '10:00', 'close' => '15:00'],
                        'sunday' => ['open' => 'closed', 'close' => 'closed']
                    ],
                    'photos' => [
                        "https://via.placeholder.com/640x480.png/business-$i-1",
                        "https://via.placeholder.com/640x480.png/business-$i-2"
                    ],
                    'services' => [
                        'Professional Service',
                        'Quality Work'
                    ],
                    'reviews' => $reviews,
                    'rating' => $rating, // Set top-level rating
                    'lat' => '40.7128',
                    'lng' => '-74.0060',
                ]);
                $discoveredCount++;
            } catch (\Exception $e) {
                Log::error('Failed to create scraped business', [
                    'scraping_job_id' => $scrapingJob->id,
                    'business_index' => $i,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Scraping completed successfully', [
            'scraping_job_id' => $scrapingJob->id,
            'location_query' => $locationQuery,
            'category_query' => $categoryQuery,
            'discovered_businesses_count' => $discoveredCount
        ]);

        $scrapingJob->update([
            'discovered_businesses_count' => $discoveredCount,
            'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
            'message' => "Simulated scraping completed successfully. Found $discoveredCount businesses."
        ]);

        Log::info('Simulated scraping finished.', ['scraping_job_id' => $scrapingJob->id]);
    }

    /**
     * Find existing businesses in the area that might need data enhancement
     *
     * @param string $zipCode
     * @param string|null $category
     * @return Collection
     */
    protected function findExistingBusinessesInArea(string $zipCode, ?string $category = null): Collection
    {
        // Use BusinessDiscoveryService to find businesses, but don't filter by email
        // since we want to find businesses that might be missing email or other data
        $businessDiscoveryService = app(BusinessDiscoveryService::class);

        // Get all businesses in the area, not just those with complete data
        $query = Business::query();

        // Get zip codes within radius
        $zipCodeService = app(\App\Services\ZipCode\ZipCode::class);
        $radius = config('job_notification.default_radius', 30);
        $zipCodes = $zipCodeService->getZipCodesInRadiusRaw($zipCode, $radius);

        if (empty($zipCodes)) {
            $query->where('zip_code', $zipCode);
        } else {
            $query->whereIn('zip_code', $zipCodes);
        }

        // Filter by category if provided
        if (!is_null($category)) {
            $query->where('category', $category);
        }

        return $query->get();
    }

    /**
     * Enhance existing businesses with incomplete data
     *
     * @param ScrapingJob $scrapingJob
     * @param Collection $businesses
     * @return void
     */
    protected function enhanceExistingBusinesses(ScrapingJob $scrapingJob, Collection $businesses): void
    {
        $incompleteBusinesses = $this->completenessService->filterIncompleteBusinesses($businesses);

        if ($incompleteBusinesses->isEmpty()) {
            Log::info('All existing businesses have complete data', [
                'scraping_job_id' => $scrapingJob->id,
                'total_businesses' => $businesses->count(),
            ]);

            $scrapingJob->update([
                'discovered_businesses_count' => 0,
                'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                'message' => 'All existing businesses already have complete data.'
            ]);

            return;
        }

        Log::info('Found businesses needing data enhancement', [
            'scraping_job_id' => $scrapingJob->id,
            'incomplete_businesses_count' => $incompleteBusinesses->count(),
            'total_businesses' => $businesses->count(),
        ]);

        $enhancedCount = 0;

        foreach ($incompleteBusinesses as $business) {
            $missingFields = $this->completenessService->getMissingCriticalFields($business);

            if (!empty($missingFields)) {
                Log::info('Dispatching enhancement job for business', [
                    'business_id' => $business->id,
                    'business_name' => $business->name,
                    'missing_fields' => $missingFields,
                ]);

                // Dispatch enhancement job for this business
                EnhanceBusinessDataJob::dispatch($business, $missingFields, $scrapingJob);
                $enhancedCount++;
            }
        }

        if ($enhancedCount > 0) {
            $scrapingJob->update([
                'discovered_businesses_count' => $enhancedCount,
                'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                'message' => "Dispatched enhancement jobs for {$enhancedCount} businesses with incomplete data."
            ]);
        } else {
            // If no businesses needed enhancement, try scraping new ones
            Log::info('No businesses needed enhancement, falling back to new business scraping', [
                'scraping_job_id' => $scrapingJob->id,
            ]);

            $this->simulateScraping($scrapingJob);
        }
    }

    /**
     * Enhance specific fields for a business
     *
     * @param Business $business
     * @param array $fieldsToEnhance
     * @return array Enhanced data
     */
    public function enhanceBusinessFields(Business $business, array $fieldsToEnhance): array
    {
        $enhancedData = [];

        Log::info('Enhancing specific fields for business', [
            'business_id' => $business->id,
            'fields_to_enhance' => $fieldsToEnhance,
        ]);

        // This is where real scraping logic would go
        // For now, simulate enhancement with realistic data
        foreach ($fieldsToEnhance as $field) {
            switch ($field) {
                case 'email':
                    if (empty($business->email)) {
                        $enhancedData['email'] = $this->generateRealisticEmail($business);
                    }
                    break;

                case 'lat':
                case 'lng':
                    if (empty($business->lat) || empty($business->lng)) {
                        $coordinates = $this->generateCoordinatesFromAddress($business);
                        if ($coordinates) {
                            $enhancedData['lat'] = $coordinates['lat'];
                            $enhancedData['lng'] = $coordinates['lng'];
                        }
                    }
                    break;

                case 'address':
                    if (empty($business->address) || !preg_match('/\b\d{5}\b/', $business->address)) {
                        $enhancedData['address'] = $this->enhanceAddress($business);
                    }
                    break;

                case 'phone':
                    if (empty($business->phone)) {
                        $enhancedData['phone'] = $this->generateRealisticPhone();
                    }
                    break;

                case 'website':
                    if (empty($business->website)) {
                        $enhancedData['website'] = $this->generateRealisticWebsite($business);
                    }
                    break;
            }
        }

        return $enhancedData;
    }

    /**
     * Generate a realistic email for the business
     *
     * @param Business $business
     * @return string
     */
    protected function generateRealisticEmail(Business $business): string
    {
        $businessName = strtolower(str_replace(' ', '', $business->name ?? 'business'));
        $businessName = preg_replace('/[^a-z0-9]/', '', $businessName);
        $domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'business.com'];

        return substr($businessName, 0, 10) . '@' . $domains[array_rand($domains)];
    }

    /**
     * Generate coordinates from address using geocoding simulation
     *
     * @param Business $business
     * @return array|null
     */
    protected function generateCoordinatesFromAddress(Business $business): ?array
    {
        $location = $business->location ?? $business->address ?? '';

        // Default to coordinates around major US cities
        $cityCoordinates = [
            'new york' => ['lat' => 40.7128, 'lng' => -74.0060],
            'los angeles' => ['lat' => 34.0522, 'lng' => -118.2437],
            'chicago' => ['lat' => 41.8781, 'lng' => -87.6298],
            'houston' => ['lat' => 29.7604, 'lng' => -95.3698],
            'phoenix' => ['lat' => 33.4484, 'lng' => -112.0740],
        ];

        foreach ($cityCoordinates as $city => $coords) {
            if (stripos($location, $city) !== false) {
                return [
                    'lat' => $coords['lat'] + (rand(-100, 100) / 10000),
                    'lng' => $coords['lng'] + (rand(-100, 100) / 10000),
                ];
            }
        }

        // Default coordinates with random offset
        return [
            'lat' => 39.8283 + (rand(-500, 500) / 10000),
            'lng' => -98.5795 + (rand(-500, 500) / 10000),
        ];
    }

    /**
     * Enhance address with proper zip code
     *
     * @param Business $business
     * @return string
     */
    protected function enhanceAddress(Business $business): string
    {
        $currentAddress = $business->address ?? $business->location ?? '';

        if (preg_match('/\b\d{5}\b/', $currentAddress)) {
            return $currentAddress;
        }

        $zipCode = str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT);
        return trim($currentAddress) . ', ' . $zipCode;
    }

    /**
     * Generate a realistic phone number
     *
     * @return string
     */
    protected function generateRealisticPhone(): string
    {
        $areaCode = rand(200, 999);
        $exchange = rand(200, 999);
        $number = rand(1000, 9999);

        return "({$areaCode}) {$exchange}-{$number}";
    }

    /**
     * Generate a realistic website URL
     *
     * @param Business $business
     * @return string
     */
    protected function generateRealisticWebsite(Business $business): string
    {
        $businessName = strtolower(str_replace(' ', '', $business->name ?? 'business'));
        $businessName = preg_replace('/[^a-z0-9]/', '', $businessName);

        return 'https://www.' . substr($businessName, 0, 15) . '.com';
    }

    /**
     * Process the job notification campaign after scraping is complete.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function processCampaignAfterScraping(ScrapingJob $scrapingJob): void
    {
        $campaign = $scrapingJob->jobNotificationCampaign;

        if ($scrapingJob->discovered_businesses_count > 0) {
            Log::info('Re-processing job notification campaign after successful scraping.', [
                'campaign_id' => $campaign->id,
                'discovered_businesses_count' => $scrapingJob->discovered_businesses_count
            ]);

            // Use the JobNotificationService to re-run business discovery
            // This will find the newly scraped businesses and set up the campaign
            $businessesFound = $this->jobNotificationService->processBusinessDiscovery(
                $campaign,
                $campaign->job_zip_code,
                $campaign->job_category,
                $campaign->search_radius
            );

            if ($businessesFound) {
                Log::info('Campaign successfully re-processed after scraping', [
                    'campaign_id' => $campaign->id,
                    'business_count' => $campaign->business_count
                ]);
            } else {
                Log::warning('No businesses found during re-processing after scraping', [
                    'scraping_job_id' => $scrapingJob->id,
                    'campaign_id' => $campaign->id,
                ]);
                $campaign->status = \App\Enums\JobNotificationStatusEnum::REJECTED;
                $campaign->rejection_reason = 'No businesses found even after scraping and re-processing.';
                $campaign->save();
            }
        } else {
            Log::warning('Scraping finished but no businesses were found.', [
                'scraping_job_id' => $scrapingJob->id,
                'campaign_id' => $campaign->id,
            ]);
            $campaign->status = \App\Enums\JobNotificationStatusEnum::REJECTED;
            $campaign->rejection_reason = 'No businesses found even after scraping.';
            $campaign->save();
        }
    }
} 