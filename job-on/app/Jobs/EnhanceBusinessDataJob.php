<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Business;
use App\Models\ScrapingJob;
use App\Services\BusinessDataCompletenessService;
use App\Services\ScrapingService;
use App\Enums\ScrapingJobStatusEnum;

class EnhanceBusinessDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The business to enhance
     *
     * @var Business
     */
    protected $business;

    /**
     * The scraping job associated with this enhancement
     *
     * @var ScrapingJob|null
     */
    protected $scrapingJob;

    /**
     * Fields that need to be enhanced
     *
     * @var array
     */
    protected $fieldsToEnhance;

    /**
     * Create a new job instance.
     *
     * @param Business $business
     * @param array $fieldsToEnhance
     * @param ScrapingJob|null $scrapingJob
     */
    public function __construct(Business $business, array $fieldsToEnhance = [], ?ScrapingJob $scrapingJob = null)
    {
        $this->business = $business;
        $this->fieldsToEnhance = $fieldsToEnhance;
        $this->scrapingJob = $scrapingJob;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(BusinessDataCompletenessService $completenessService, ScrapingService $scrapingService): void
    {
        Log::info('Starting business data enhancement', [
            'business_id' => $this->business->id,
            'business_name' => $this->business->name,
            'fields_to_enhance' => $this->fieldsToEnhance,
            'attempt' => $this->attempts(),
        ]);

        try {
            // Refresh the business model to get latest data
            $this->business->refresh();

            // If no specific fields provided, determine what needs enhancement
            if (empty($this->fieldsToEnhance)) {
                $this->fieldsToEnhance = $completenessService->getMissingCriticalFields($this->business);
            }

            if (empty($this->fieldsToEnhance)) {
                Log::info('Business data is already complete, no enhancement needed', [
                    'business_id' => $this->business->id,
                ]);
                return;
            }

            // Update scraping job status if provided
            if ($this->scrapingJob) {
                $this->scrapingJob->update([
                    'status' => ScrapingJobStatusEnum::IN_PROGRESS,
                    'message' => "Enhancing business data for {$this->business->name} (Attempt {$this->attempts()}/{$this->tries})",
                ]);
            }

            // Perform the enhancement
            $enhancedData = $this->enhanceBusinessData();

            if (!empty($enhancedData)) {
                // Update the business with enhanced data
                $this->business->update($enhancedData);

                Log::info('Business data enhanced successfully', [
                    'business_id' => $this->business->id,
                    'enhanced_fields' => array_keys($enhancedData),
                    'enhanced_data' => $enhancedData,
                ]);

                // Update scraping job status if provided
                if ($this->scrapingJob) {
                    $this->scrapingJob->update([
                        'status' => ScrapingJobStatusEnum::COMPLETED,
                        'message' => "Successfully enhanced business data for {$this->business->name}",
                        'discovered_businesses_count' => 1, // One business enhanced
                    ]);
                }
            } else {
                Log::warning('No data could be enhanced for business', [
                    'business_id' => $this->business->id,
                    'fields_attempted' => $this->fieldsToEnhance,
                ]);

                if ($this->scrapingJob) {
                    $this->scrapingJob->update([
                        'status' => ScrapingJobStatusEnum::FAILED,
                        'message' => "Could not enhance data for {$this->business->name} - no additional data found",
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Business data enhancement failed', [
                'business_id' => $this->business->id,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);

            if ($this->scrapingJob) {
                $this->scrapingJob->update([
                    'status' => ScrapingJobStatusEnum::FAILED,
                    'message' => "Enhancement failed for {$this->business->name}: {$e->getMessage()}",
                ]);
            }

            throw $e;
        }
    }

    /**
     * Enhance business data by scraping missing fields
     *
     * @return array Enhanced data
     */
    protected function enhanceBusinessData(): array
    {
        $enhancedData = [];

        // Use ScrapingService for real data enhancement instead of generating fake data
        $scrapingService = app(ScrapingService::class);

        Log::info('Delegating business enhancement to ScrapingService', [
            'business_id' => $this->business->id,
            'fields_to_enhance' => $this->fieldsToEnhance
        ]);

        // Use the ScrapingService's real enhancement methods
        $enhancedData = $scrapingService->enhanceBusinessFields($this->business, $this->fieldsToEnhance);

        if (empty($enhancedData)) {
            Log::warning('No real data could be enhanced for business', [
                'business_id' => $this->business->id,
                'fields_attempted' => $this->fieldsToEnhance,
            ]);
        }

        return $enhancedData;
    }



    public function failed(\Throwable $exception): void
    {
        Log::error('EnhanceBusinessDataJob failed permanently', [
            'business_id' => $this->business->id,
            'fields_to_enhance' => $this->fieldsToEnhance,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        if ($this->scrapingJob) {
            $this->scrapingJob->update([
                'status' => ScrapingJobStatusEnum::FAILED,
                'message' => "Enhancement failed permanently for {$this->business->name}: {$exception->getMessage()}",
            ]);
        }
    }
}
