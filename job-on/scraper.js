#!/usr/bin/env node

const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");
const cheerio = require("cheerio");

// Configuration
const config = {
  userAgent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  delayBetweenRequests: 2000,
  delayBetweenBusinesses: 1000,
  maxRetries: 3,
  retryDelay: 5000,
  maxBusinessesPerCategory: 20,
};

// Function to validate that business data is real and not placeholder
function isValidBusinessData(business) {
  // Must have a name
  if (!business.name || business.name.trim() === "") {
    return false;
  }

  // Skip businesses with placeholder/sample names
  const placeholderNames = [
    "sample business",
    "test business",
    "placeholder",
    "example business",
    "demo business",
  ];

  const lowerName = business.name.toLowerCase();
  if (placeholderNames.some((placeholder) => lowerName.includes(placeholder))) {
    return false;
  }

  // Must have at least one piece of contact information
  const hasContactInfo =
    business.address || business.phone || business.website || business.email;
  if (!hasContactInfo) {
    return false;
  }

  // Skip businesses with obviously fake data
  if (
    business.phone &&
    (business.phone.includes("555-000") ||
      business.phone.includes("123-456") ||
      business.phone === "************")
  ) {
    return false;
  }

  if (
    business.email &&
    (business.email.includes("@scraped.com") ||
      business.email.includes("@sample.com") ||
      business.email.includes("@test.com"))
  ) {
    return false;
  }

  if (
    business.website &&
    (business.website.includes("sample-business.com") ||
      business.website.includes("placeholder.com") ||
      business.website.includes("example.com"))
  ) {
    return false;
  }

  return true;
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    location: "",
    category: "",
    maxBusinesses: 20,
    outputFile: "scraped_businesses.json",
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === "--location" || arg === "-l") {
      options.location = args[++i];
    } else if (arg === "--category" || arg === "-c") {
      options.category = args[++i];
    } else if (arg === "--max" || arg === "-m") {
      options.maxBusinesses = parseInt(args[++i]) || 20;
    } else if (arg === "--output" || arg === "-o") {
      options.outputFile = args[++i];
    } else if (arg === "--help" || arg === "-h") {
      console.log(`
Usage: node scraper.js [options]

Options:
  --location, -l    Location to search (required)
  --category, -c    Business category to search (required)
  --max, -m         Maximum number of businesses to scrape (default: 20)
  --output, -o      Output file path (default: scraped_businesses.json)
  --help, -h        Show this help message

Example:
  node scraper.js --location "Oakland, CA" --category "Plumbing" --max 10 --output results.json
      `);
      process.exit(0);
    }
  }

  if (!options.location || !options.category) {
    console.error("Error: Both --location and --category are required");
    console.error("Use --help for usage information");
    process.exit(1);
  }

  return options;
}

// Helper function to scroll down the page to load more results
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      let scrollAttempts = 0;
      let lastScrollHeight = 0;
      const maxScrollAttempts = 50;

      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;
        scrollAttempts++;

        if (
          totalHeight >= scrollHeight - window.innerHeight ||
          (scrollHeight === lastScrollHeight && scrollAttempts > 5) ||
          scrollAttempts >= maxScrollAttempts
        ) {
          clearInterval(timer);
          resolve();
        }

        lastScrollHeight = scrollHeight;
      }, 200);
    });
  });

  await new Promise((resolve) => setTimeout(resolve, 2000));
}

// Main scraping function
async function scrapeBusinesses(location, category, maxBusinesses, outputFile) {
  const browser = await puppeteer.launch({
    headless: "new",
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--disable-gpu",
    ],
    protocolTimeout: 60000,
  });

  const businesses = [];

  try {
    console.log(`Starting to scrape ${category} businesses in ${location}...`);

    const page = await browser.newPage();
    await page.setUserAgent(config.userAgent);

    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
    });

    // Construct the Google Maps search URL
    const searchTerm = encodeURIComponent(`${category} near ${location}`);
    const url = `https://www.google.com/maps/search/${searchTerm}/`;

    console.log(`Visiting: ${url}`);

    // Navigate to the page
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for business listings to load
    const selectors = [
      'div[role="article"]',
      'div[jsaction*="mouseover:pane"]',
      'a[href^="https://www.google.com/maps/place"]',
    ];

    let listingsFound = false;
    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 10000 });
        console.log(`Found business listings with selector: ${selector}`);
        listingsFound = true;
        break;
      } catch (error) {
        console.log(`Selector ${selector} not found, trying next...`);
      }
    }

    if (!listingsFound) {
      throw new Error("No business listings found");
    }

    // Scroll to load more results
    console.log("Scrolling to load more results...");
    await autoScroll(page);

    // Get page content and parse with cheerio
    const content = await page.content();
    const $ = cheerio.load(content);

    // Find business listings
    let businessListings = $('div[role="article"]');
    if (businessListings.length === 0) {
      businessListings = $('div[jsaction*="mouseover:pane"]');
    }
    if (businessListings.length === 0) {
      businessListings = $(
        'a[href^="https://www.google.com/maps/place"]'
      ).closest("div");
    }

    console.log(`Found ${businessListings.length} business listings`);

    // Process each business listing
    let processedCount = 0;
    for (
      let i = 0;
      i < businessListings.length && processedCount < maxBusinesses;
      i++
    ) {
      const listing = $(businessListings[i]);

      // Extract business name
      let name = "";
      const nameElement = listing.find(
        'h3, div[role="heading"], div.fontHeadlineSmall'
      );
      if (nameElement.length) {
        name = nameElement.first().text().trim();
      }

      if (!name) {
        console.log("Could not find business name, skipping");
        continue;
      }

      console.log(`Processing business: ${name}`);

      // Extract basic information from the listing
      const business = {
        name,
        category,
        location,
        address: "",
        phone: "",
        website: "",
        email: "",
        hours: [],
        photos: [],
        services: [],
        reviews: [],
        rating: 0,
      };

      // Try to extract additional details from the listing
      // This is a simplified version - in a full implementation,
      // you would click on each business to get detailed information

      // For now, we'll just collect the basic information available in the listing
      const addressElement = listing.find('span:contains("·")').first();
      if (addressElement.length) {
        business.address = addressElement.text().trim();
      }

      // Validate the business data
      if (isValidBusinessData(business)) {
        businesses.push(business);
        processedCount++;
        console.log(`Added business: ${name}`);
      } else {
        console.log(`Skipping business with insufficient data: ${name}`);
      }

      // Small delay between processing businesses
      await new Promise((resolve) =>
        setTimeout(resolve, config.delayBetweenBusinesses)
      );
    }

    await page.close();
  } catch (error) {
    console.error("Error during scraping:", error);
    throw error;
  } finally {
    await browser.close();
  }

  // Save results to file
  console.log(`Saving ${businesses.length} businesses to ${outputFile}`);
  fs.writeFileSync(outputFile, JSON.stringify(businesses, null, 2));

  return businesses;
}

// Main execution
async function main() {
  try {
    const options = parseArgs();

    console.log("Starting business scraper with options:");
    console.log(`Location: ${options.location}`);
    console.log(`Category: ${options.category}`);
    console.log(`Max businesses: ${options.maxBusinesses}`);
    console.log(`Output file: ${options.outputFile}`);
    console.log("");

    const businesses = await scrapeBusinesses(
      options.location,
      options.category,
      options.maxBusinesses,
      options.outputFile
    );

    console.log(`\nScraping completed successfully!`);
    console.log(`Found ${businesses.length} valid businesses`);
    console.log(`Results saved to: ${options.outputFile}`);

    // Exit with success code
    process.exit(0);
  } catch (error) {
    console.error("Scraping failed:", error.message);
    process.exit(1);
  }
}

// Run the scraper
if (require.main === module) {
  main();
}

module.exports = { scrapeBusinesses, isValidBusinessData };
