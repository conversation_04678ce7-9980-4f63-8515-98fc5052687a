const puppeteer = require("puppeteer");
const fs = require("fs");
const axios = require("axios");
const cheerio = require("cheerio");
const path = require("path");

// Read locations from CSV file
function readLocationsFromCSV(maxLocations = 50, startIndex = 0) {
  try {
    const csvPath = path.resolve(__dirname, "us-cities.csv");
    console.log(`Reading locations from CSV file: ${csvPath}`);

    const fileContent = fs.readFileSync(csvPath, "utf8");
    const lines = fileContent.split("\n");

    // Skip header line and process each row
    const locations = [];
    const endIndex = Math.min(lines.length, startIndex + maxLocations);

    for (let i = startIndex + 1; i < endIndex; i++) {
      const line = lines[i].trim();
      if (line) {
        const [city, state] = line.split(",");
        if (city && state) {
          locations.push(`${city}, ${state}`);
        }
      }
    }

    console.log(
      `Loaded ${
        locations.length
      } locations from CSV file (batch from index ${startIndex} to ${
        endIndex - 1
      })`
    );

    // Return the locations and the total number of lines in the CSV
    return {
      locations: locations,
      totalLocations: lines.length - 1, // Subtract 1 for the header
      nextIndex: endIndex,
    };
  } catch (error) {
    console.error("Error reading locations from CSV:", error);
    return {
      locations: [],
      totalLocations: 0,
      nextIndex: 0,
    };
  }
}

// Define categories
const categories = ["Plumbing", "Electrical", "Cleaning", "Landscaping"];

// Configuration
const config = {
  outputFile: path.resolve(__dirname, "business_data.json"),
  tempFile: path.resolve(__dirname, "temp_business_data.json"),
  userAgent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  delayBetweenRequests: 2000,
  delayBetweenBusinesses: 1000,
  maxRetries: 3,
  retryDelay: 5000,
  maxBusinessesPerCategory: 100, // Added to ensure all businesses are scraped
  maxLocations: 50, // Maximum number of locations to process from CSV file
  // saveInterval removed - now saving immediately after each business is scraped
};

// Log the absolute file paths
console.log(`Output file path: ${config.outputFile}`);
console.log(`Temp file path: ${config.tempFile}`);

// Initialize locations from CSV file
let locationsData = readLocationsFromCSV(config.maxLocations, 0);
let locations = locationsData.locations;
const totalLocations = locationsData.totalLocations;
let nextLocationIndex = locationsData.nextIndex;

console.log(
  `Total locations in CSV: ${totalLocations}. Processing first batch of ${locations.length} locations.`
);

// Function to save progress to temp file and output file
function saveProgress(businesses, processedPairs, locationIndex = null) {
  console.log(
    `Attempting to save progress. Total businesses: ${businesses.length}`
  );

  try {
    // Save to temp file for resuming if needed
    console.log(`Writing to temp file: ${config.tempFile}`);
    fs.writeFileSync(
      config.tempFile,
      JSON.stringify(
        {
          businesses: businesses,
          processedPairs: Array.from(processedPairs),
          locationIndex:
            locationIndex !== null ? locationIndex : nextLocationIndex,
        },
        null,
        2
      )
    );
    console.log(`Successfully wrote to temp file: ${config.tempFile}`);

    // Verify temp file was created
    if (fs.existsSync(config.tempFile)) {
      console.log(`Temp file exists after writing: ${config.tempFile}`);
    } else {
      console.error(
        `Temp file does not exist after writing: ${config.tempFile}`
      );
    }

    // Also save to output file so it's visible after first 5 items
    console.log(`Writing to output file: ${config.outputFile}`);
    fs.writeFileSync(config.outputFile, JSON.stringify(businesses, null, 2));
    console.log(`Successfully wrote to output file: ${config.outputFile}`);

    // Verify output file was created
    if (fs.existsSync(config.outputFile)) {
      console.log(`Output file exists after writing: ${config.outputFile}`);
    } else {
      console.error(
        `Output file does not exist after writing: ${config.outputFile}`
      );
    }

    console.log(`Progress saved. Total businesses: ${businesses.length}`);
    console.log(`Data written to ${config.outputFile}`);
  } catch (error) {
    console.error("Error saving progress:", error);
  }
}

// Function to add a single business and save immediately
function addAndSaveBusiness(business, allBusinesses, processedPairs) {
  // Add the business to the array
  allBusinesses.push(business);

  // Save progress immediately
  saveProgress(allBusinesses, processedPairs);

  console.log(
    `Business "${business.name}" saved immediately. Total businesses: ${allBusinesses.length}`
  );
}

// Main function to scrape business data
async function scrapeBusinessData() {
  // Check if temp file exists to resume scraping
  let allBusinesses = [];
  let processedPairs = new Set();

  // Test file system access at the beginning
  try {
    console.log("Testing file system access...");
    fs.writeFileSync(
      "test_access.json",
      JSON.stringify({ test: "access" }),
      "utf8"
    );
    console.log("Successfully wrote test file. File system access is working.");
    if (fs.existsSync("test_access.json")) {
      console.log("Test file exists after writing.");
      fs.unlinkSync("test_access.json");
      console.log("Test file removed.");
    } else {
      console.error("Test file does not exist after writing!");
    }
  } catch (error) {
    console.error("Error testing file system access:", error);
  }

  if (fs.existsSync(config.tempFile)) {
    try {
      const tempData = JSON.parse(fs.readFileSync(config.tempFile, "utf8"));
      allBusinesses = tempData.businesses || [];
      processedPairs = new Set(tempData.processedPairs || []);

      // Resume from the saved location index if available
      if (tempData.locationIndex !== undefined && tempData.locationIndex > 0) {
        nextLocationIndex = tempData.locationIndex;
        // Load the locations from the saved index
        locationsData = readLocationsFromCSV(
          config.maxLocations,
          nextLocationIndex
        );
        locations = locationsData.locations;
        console.log(
          `Resuming from location index ${nextLocationIndex}. Loaded ${locations.length} locations.`
        );
      }

      console.log(
        `Resuming scraping. Already processed ${processedPairs.size} location-category pairs and found ${allBusinesses.length} businesses.`
      );
      console.log(
        `Will continue scraping and save progress immediately after each new business is scraped.`
      );
    } catch (error) {
      console.error("Error reading temp file:", error);
    }
  } else {
    console.log(
      `Starting new scraping session. Will save progress immediately after each business is scraped.`
    );
  }

  const browser = await puppeteer.launch({
    headless: "new",
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--disable-gpu",
    ],
    protocolTimeout: 60000, // Increased protocol timeout to 60 seconds to prevent Target.createTarget timeout
  });

  try {
    console.log("Starting to scrape business data...");

    // Initialize progress tracking without sample data
    console.log("Starting real business scraping without sample data...");

    // Save initial progress (empty if starting fresh)
    saveProgress(allBusinesses, processedPairs, nextLocationIndex);

    // Process all locations in batches
    let currentBatchIndex = 0;
    let hasMoreLocations = true;

    while (hasMoreLocations) {
      console.log(
        `Processing batch ${currentBatchIndex + 1} of locations (${
          locations.length
        } locations)`
      );

      for (const location of locations) {
        console.log(`Processing location: ${location}`);

        for (const category of categories) {
          const pairKey = `${location}|${category}`;

          // Skip if already processed
          if (processedPairs.has(pairKey)) {
            console.log(
              `  Skipping already processed: ${category} in ${location}`
            );
            continue;
          }

          console.log(`  Processing category: ${category}`);

          let businesses = [];
          let retries = 0;
          let success = false;

          // Retry logic for network issues
          while (!success && retries < config.maxRetries) {
            try {
              businesses = await scrapeGoogleMapsBusinesses(
                browser,
                location,
                category,
                allBusinesses,
                processedPairs
              );
              success = true;
            } catch (error) {
              retries++;
              console.error(
                `  Error scraping (attempt ${retries}/${config.maxRetries}):`,
                error.message
              );

              if (retries < config.maxRetries) {
                console.log(
                  `  Retrying in ${config.retryDelay / 1000} seconds...`
                );
                await new Promise((resolve) =>
                  setTimeout(resolve, config.retryDelay)
                );
              }
            }
          }

          if (success) {
            // We no longer need to add businesses to the main array here
            // as they are added and saved individually in scrapeGoogleMapsBusinesses
            processedPairs.add(pairKey);

            console.log(`  Total businesses so far: ${allBusinesses.length}`);
          } else {
            console.error(
              `  Failed to scrape ${category} in ${location} after ${config.maxRetries} attempts. Skipping.`
            );
          }

          // Add a small delay to avoid rate limiting
          await new Promise((resolve) =>
            setTimeout(resolve, config.delayBetweenRequests)
          );
        }
      }

      // Check if there are more locations to process
      if (nextLocationIndex < totalLocations) {
        currentBatchIndex++;
        console.log(
          `Loading next batch of locations starting from index ${nextLocationIndex}`
        );

        // Load the next batch of locations
        locationsData = readLocationsFromCSV(
          config.maxLocations,
          nextLocationIndex
        );
        locations = locationsData.locations;
        nextLocationIndex = locationsData.nextIndex;

        console.log(
          `Loaded next batch of ${locations.length} locations. Total processed so far: ${nextLocationIndex}`
        );

        // Save progress before starting the next batch
        saveProgress(allBusinesses, processedPairs, nextLocationIndex);
      } else {
        hasMoreLocations = false;
        console.log(`All ${totalLocations} locations have been processed.`);
      }
    }

    // Final save to ensure all data is written
    saveProgress(allBusinesses, processedPairs, nextLocationIndex);
    console.log(
      `Scraping completed. Found ${allBusinesses.length} businesses.`
    );

    // Clean up temp file
    if (fs.existsSync(config.tempFile)) {
      fs.unlinkSync(config.tempFile);
      console.log("Temporary file removed.");
    }
  } catch (error) {
    console.error("Error during scraping:", error);

    // Save progress in case of unexpected error (without adding sample data)
    saveProgress(allBusinesses, processedPairs, nextLocationIndex);
    console.log(
      `Progress saved with ${allBusinesses.length} real businesses. You can resume later.`
    );
  } finally {
    await browser.close();
  }
}

// Function to scrape businesses from Google Maps for a specific location and category
async function scrapeGoogleMapsBusinesses(
  browser,
  location,
  category,
  allBusinesses,
  processedPairs
) {
  const businesses = [];
  const page = await browser.newPage();
  let totalProcessedCount = 0;
  let currentPage = 1;
  const maxPages = 5; // Maximum number of pages to scrape

  try {
    // Set user agent to avoid being blocked
    await page.setUserAgent(config.userAgent);

    // Set extra HTTP headers
    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
    });

    // Construct the Google Maps search URL
    const searchTerm = encodeURIComponent(`${category} near ${location}`);
    const url = `https://www.google.com/maps/search/${searchTerm}/`;

    console.log(`    Visiting: ${url}`);

    // Navigate to the page with timeout and retry
    let pageLoaded = false;
    let pageLoadAttempts = 0;

    while (!pageLoaded && pageLoadAttempts < 3) {
      try {
        await page.goto(url, {
          waitUntil: "networkidle2",
          timeout: 30000,
        });
        pageLoaded = true;
      } catch (error) {
        pageLoadAttempts++;
        console.error(
          `    Error loading page (attempt ${pageLoadAttempts}/3):`,
          error.message
        );

        if (pageLoadAttempts < 3) {
          console.log(`    Retrying page load in 5 seconds...`);
          await new Promise((resolve) => setTimeout(resolve, 5000));
        } else {
          throw new Error(
            `Failed to load page after 3 attempts: ${error.message}`
          );
        }
      }
    }

    // Wait for the business listings to load with multiple selector options
    const selectors = [
      'div[role="article"]',
      'div[jsaction*="mouseover:pane"]',
      'a[href^="https://www.google.com/maps/place"]',
    ];

    let listingsFound = false;

    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 10000 });
        console.log(`    Found business listings with selector: ${selector}`);
        listingsFound = true;
        break;
      } catch (error) {
        console.log(`    Selector ${selector} not found, trying next...`);
      }
    }

    if (!listingsFound) {
      console.log(
        "    No business listings found with any selector. Taking a screenshot for debugging."
      );
      await page.screenshot({
        path: `debug_${category}_${location.replace(/[^a-zA-Z0-9]/g, "_")}.png`,
      });
      throw new Error("No business listings found");
    }

    // Scroll down to load more results
    console.log("    Scrolling to load more results...");
    await autoScroll(page);

    // Get the page content
    const content = await page.content();
    const $ = cheerio.load(content);

    // Try different selectors for business listings
    let businessListings = $('div[role="article"]');

    if (businessListings.length === 0) {
      businessListings = $('div[jsaction*="mouseover:pane"]');
    }

    if (businessListings.length === 0) {
      businessListings = $(
        'a[href^="https://www.google.com/maps/place"]'
      ).closest("div");
    }

    console.log(
      `    Found ${businessListings.length} business listings on page ${currentPage}`
    );

    // Process each business listing
    let processedCount = 0;
    for (
      let i = 0;
      i < businessListings.length &&
      totalProcessedCount < config.maxBusinessesPerCategory;
      i++
    ) {
      // Limit based on config
      const listing = $(businessListings[i]);

      // Extract business name
      let name = "";
      const nameElement = listing.find(
        'h3, div[role="heading"], div.fontHeadlineSmall'
      );

      if (nameElement.length) {
        name = nameElement.first().text().trim();
      }

      if (!name) {
        console.log("    Could not find business name, skipping");
        continue; // Skip if no name found
      }

      processedCount++;
      totalProcessedCount++;

      // Check if we've reached the maximum number of businesses
      if (totalProcessedCount >= config.maxBusinessesPerCategory) {
        console.log(
          `    Reached maximum number of businesses (${config.maxBusinessesPerCategory}). Stopping.`
        );
        break;
      }

      // Extract business URL to get more details
      let businessUrl = "";
      const hrefElement = listing.find(
        'a[href^="https://www.google.com/maps/place"]'
      );

      if (hrefElement.length) {
        businessUrl = hrefElement.attr("href");
      } else {
        // Try to get the URL by clicking on the listing
        try {
          // Click on the listing to open the details panel
          await page.click(`h3:contains("${name}")`);
          await page.waitForSelector('div[role="dialog"]', { timeout: 5000 });

          // Get the URL from the browser
          businessUrl = page.url();
        } catch (error) {
          console.log(
            `    Could not find or click business URL for ${name}, skipping`
          );
          continue;
        }
      }

      console.log(`    Processing business: ${name} (${businessUrl})`);

      // Get detailed business information
      try {
        const businessDetails = await scrapeGoogleMapsBusinessDetails(
          browser,
          businessUrl,
          name
        );

        // Create business object with all available information
        const business = {
          name,
          category,
          location,
          ...businessDetails,
        };

        // Validate that we have real business data (not empty or placeholder)
        if (isValidBusinessData(business)) {
          // Add to local array for tracking
          businesses.push(business);

          // Add to main array and save immediately
          addAndSaveBusiness(business, allBusinesses, processedPairs);
          console.log(`    Scraped: ${name}`);
        } else {
          console.log(`    Skipping business with insufficient data: ${name}`);
        }
      } catch (error) {
        console.error(`    Error scraping details for ${name}:`, error.message);
      }

      // Add a small delay between scraping business details
      await new Promise((resolve) =>
        setTimeout(resolve, config.delayBetweenBusinesses)
      );
    }

    // Check if we should proceed to the next page
    if (
      totalProcessedCount < config.maxBusinessesPerCategory &&
      currentPage < maxPages
    ) {
      console.log(
        `    Checking for next page (current: ${currentPage}, max: ${maxPages})...`
      );

      // Look for the "Next page" button
      const nextPageButton = await page.$(
        'button[aria-label="Next page"], button[jsaction*="pane.paginationSection.nextPage"]'
      );

      if (nextPageButton) {
        console.log(
          `    Found next page button. Moving to page ${currentPage + 1}...`
        );

        // Click the next page button
        await nextPageButton.click();

        // Wait for the page to load
        await page.waitForTimeout(3000);

        // Wait for the business listings to load with multiple selector options
        let nextPageListingsFound = false;
        for (const selector of selectors) {
          try {
            await page.waitForSelector(selector, { timeout: 10000 });
            nextPageListingsFound = true;
            break;
          } catch (error) {
            console.log(
              `    Selector ${selector} not found on next page, trying next...`
            );
          }
        }

        if (nextPageListingsFound) {
          // Increment the page counter
          currentPage++;

          // Scroll down to load more results
          console.log("    Scrolling to load more results on next page...");
          await autoScroll(page);

          // Get the page content
          const nextPageContent = await page.content();
          const $nextPage = cheerio.load(nextPageContent);

          // Try different selectors for business listings
          let nextPageBusinessListings = $nextPage('div[role="article"]');

          if (nextPageBusinessListings.length === 0) {
            nextPageBusinessListings = $nextPage(
              'div[jsaction*="mouseover:pane"]'
            );
          }

          if (nextPageBusinessListings.length === 0) {
            nextPageBusinessListings = $nextPage(
              'a[href^="https://www.google.com/maps/place"]'
            ).closest("div");
          }

          console.log(
            `    Found ${nextPageBusinessListings.length} business listings on page ${currentPage}`
          );

          // Process each business listing on the next page (recursive call)
          let nextPageProcessedCount = 0;
          for (
            let i = 0;
            i < nextPageBusinessListings.length &&
            totalProcessedCount < config.maxBusinessesPerCategory;
            i++
          ) {
            const listing = $nextPage(nextPageBusinessListings[i]);

            // Extract business name
            let name = "";
            const nameElement = listing.find(
              'h3, div[role="heading"], div.fontHeadlineSmall'
            );

            if (nameElement.length) {
              name = nameElement.first().text().trim();
            }

            if (!name) {
              console.log(
                "    Could not find business name on next page, skipping"
              );
              continue; // Skip if no name found
            }

            nextPageProcessedCount++;
            totalProcessedCount++;

            // Check if we've reached the maximum number of businesses
            if (totalProcessedCount >= config.maxBusinessesPerCategory) {
              console.log(
                `    Reached maximum number of businesses (${config.maxBusinessesPerCategory}). Stopping.`
              );
              break;
            }

            // Extract business URL to get more details
            let businessUrl = "";
            const hrefElement = listing.find(
              'a[href^="https://www.google.com/maps/place"]'
            );

            if (hrefElement.length) {
              businessUrl = hrefElement.attr("href");
            } else {
              console.log(
                "    Could not find business URL on next page, skipping"
              );
              continue; // Skip if no URL found
            }

            // Scrape detailed business information
            try {
              const businessDetails = await scrapeGoogleMapsBusinessDetails(
                browser,
                businessUrl,
                name
              );

              // Create business object with all available information
              const business = {
                name,
                category,
                location,
                ...businessDetails,
              };

              // Validate that we have real business data (not empty or placeholder)
              if (isValidBusinessData(business)) {
                // Add to local array for tracking
                businesses.push(business);

                // Add to main array and save immediately
                addAndSaveBusiness(business, allBusinesses, processedPairs);
                console.log(`    Scraped from next page: ${name}`);
              } else {
                console.log(
                  `    Skipping business with insufficient data from next page: ${name}`
                );
              }
            } catch (error) {
              console.error(
                `    Error scraping details for ${name} on next page:`,
                error.message
              );
            }

            // Add a small delay between scraping business details
            await new Promise((resolve) =>
              setTimeout(resolve, config.delayBetweenBusinesses)
            );
          }

          console.log(
            `    Processed ${nextPageProcessedCount} businesses on page ${currentPage}`
          );
        } else {
          console.log(
            "    No business listings found on next page. Stopping pagination."
          );
        }
      } else {
        console.log("    No next page button found. Reached the last page.");
      }
    }
  } catch (error) {
    console.error(`    Error scraping ${category} in ${location}:`, error);
    throw error; // Rethrow to trigger retry in the main function
  } finally {
    await page.close();
  }

  return businesses;
}

// Helper function to scroll down the page to load more results
async function autoScroll(page) {
  await page.evaluate(async () => {
    await new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      let scrollAttempts = 0;
      let lastScrollHeight = 0;
      const maxScrollAttempts = 50; // Maximum number of scroll attempts to prevent infinite scrolling

      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;
        scrollAttempts++;

        // Check if we've reached the bottom or if no new content has loaded after scrolling
        if (
          totalHeight >= scrollHeight - window.innerHeight ||
          (scrollHeight === lastScrollHeight && scrollAttempts > 5) ||
          scrollAttempts >= maxScrollAttempts
        ) {
          clearInterval(timer);
          resolve();
        }

        // Update last scroll height for comparison in the next iteration
        lastScrollHeight = scrollHeight;
      }, 200); // Increased delay to give more time for content to load
    });
  });

  // Wait a bit after scrolling to ensure all content is loaded
  await page.waitForTimeout(2000);
}

// Function to scrape detailed business information from a Google Maps business page
async function scrapeGoogleMapsBusinessDetails(
  browser,
  businessUrl,
  businessName
) {
  const page = await browser.newPage();
  const details = {
    address: "",
    phone: "",
    website: "",
    email: "",
    hours: [],
    photos: [],
    services: [],
    reviews: [],
  };

  try {
    // Set user agent to avoid being blocked
    await page.setUserAgent(config.userAgent);

    // Set extra HTTP headers
    await page.setExtraHTTPHeaders({
      "Accept-Language": "en-US,en;q=0.9",
      Accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Encoding": "gzip, deflate, br",
    });

    // Navigate to the page with timeout and retry
    let pageLoaded = false;
    let pageLoadAttempts = 0;

    while (!pageLoaded && pageLoadAttempts < 3) {
      try {
        await page.goto(businessUrl, {
          waitUntil: "networkidle2",
          timeout: 30000,
        });
        pageLoaded = true;
      } catch (error) {
        pageLoadAttempts++;
        console.error(
          `    Error loading business details page (attempt ${pageLoadAttempts}/3):`,
          error.message
        );

        if (pageLoadAttempts < 3) {
          console.log(
            `    Retrying business details page load in 5 seconds...`
          );
          await new Promise((resolve) => setTimeout(resolve, 5000));
        } else {
          throw new Error(
            `Failed to load business details page after 3 attempts: ${error.message}`
          );
        }
      }
    }

    // Wait for the business details to load
    try {
      await page.waitForSelector(
        'button[data-item-id="address"], button[data-item-id="phone"], div[role="region"]',
        { timeout: 10000 }
      );
    } catch (error) {
      console.log(
        "    Business details not found with expected selectors. Taking a screenshot for debugging."
      );
      await page.screenshot({
        path: `debug_details_${businessName.replace(/[^a-zA-Z0-9]/g, "_")}.png`,
      });
    }

    // Get the page content
    const content = await page.content();
    const $ = cheerio.load(content);

    // Extract address
    const addressButton = $('button[data-item-id="address"]');
    if (addressButton.length) {
      details.address = addressButton.text().trim();
    } else {
      // Try alternative selectors for address
      const addressElement = $('button[aria-label*="Address"]');
      if (addressElement.length) {
        details.address = addressElement.text().trim();
      } else {
        // Try to find address in any element containing typical address patterns
        const possibleAddressElements = $('div:contains("Address")').next();
        if (possibleAddressElements.length) {
          details.address = possibleAddressElements.first().text().trim();
        }
      }
    }

    // Extract phone number
    const phoneButton = $('button[data-item-id="phone"]');
    if (phoneButton.length) {
      details.phone = phoneButton.text().trim();
    } else {
      // Try alternative selectors for phone
      const phoneElement = $('button[aria-label*="Phone"]');
      if (phoneElement.length) {
        details.phone = phoneElement.text().trim();
      } else {
        // Look for phone numbers using regex pattern
        const phonePattern = /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/;
        const textElements = $("div, span, button");

        for (let i = 0; i < textElements.length; i++) {
          const text = $(textElements[i]).text();
          const match = text.match(phonePattern);
          if (match) {
            details.phone = match[0];
            break;
          }
        }
      }
    }

    // Extract website
    const websiteButton = $('a[data-item-id="authority"]');
    if (websiteButton.length) {
      details.website = websiteButton.attr("href");
    } else {
      // Try alternative selectors for website
      const websiteElement = $(
        'a[aria-label*="website"], a[aria-label*="Website"]'
      );
      if (websiteElement.length) {
        details.website = websiteElement.attr("href");
      } else {
        // Look for any link that might be a website
        const websiteLinks = $('a[href^="http"]:not([href*="google.com"])');
        if (websiteLinks.length) {
          for (let i = 0; i < websiteLinks.length; i++) {
            const href = $(websiteLinks[i]).attr("href");
            if (
              href &&
              !href.includes("google.com/maps") &&
              !href.includes("goo.gl/maps")
            ) {
              details.website = href;
              break;
            }
          }
        }
      }
    }

    // Try to extract email using common patterns
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    const textElements = $("div, span, button");

    for (let i = 0; i < textElements.length; i++) {
      const text = $(textElements[i]).text();
      const match = text.match(emailPattern);
      if (match) {
        details.email = match[0];
        break;
      }
    }

    // Extract hours
    const hoursSection = $(
      'div[aria-label*="Hours"], div[aria-label*="hours"]'
    );
    if (hoursSection.length) {
      // Try to find the hours table
      const hourRows = hoursSection.find("tr");
      if (hourRows.length) {
        hourRows.each((i, el) => {
          const hourText = $(el).text().trim();
          if (hourText) {
            details.hours.push(hourText);
          }
        });
      } else {
        // Try alternative approach for hours
        const hourElements = hoursSection.find('div[role="row"]');
        if (hourElements.length) {
          hourElements.each((i, el) => {
            const hourText = $(el).text().trim();
            if (
              hourText &&
              /Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday/i.test(
                hourText
              )
            ) {
              details.hours.push(hourText);
            }
          });
        } else {
          // Look for time patterns in text
          const timePattern =
            /(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)[\s\S]*?\d{1,2}:\d{2}\s*[APMapm]{2}\s*-\s*\d{1,2}:\d{2}\s*[APMapm]{2}/g;
          const fullText = hoursSection.text();
          const matches = fullText.match(timePattern);

          if (matches) {
            details.hours = matches.map((match) =>
              match.replace(/\s+/g, " ").trim()
            );
          }
        }
      }
    }

    // Extract photos (at least 3 if possible)
    console.log("    Extracting photos...");
    const minRequiredPhotos = 3; // Minimum number of photos required

    // Try to find and click on the photos section or gallery if it exists
    try {
      const photosTabExists = await page.evaluate(() => {
        const photosTab = document.querySelector(
          'button[aria-label*="photo"], a[data-item-id="photos"], a[href*="photos"], button[jsaction*="photos"]'
        );
        if (photosTab) {
          photosTab.click();
          return true;
        }
        return false;
      });

      if (photosTabExists) {
        // Wait for photos to load
        await page.waitForSelector(
          'img[src*="photo"], img[src*="places"], div[data-photo-index]',
          { timeout: 5000 }
        );

        // Get updated content after clicking
        const updatedContent = await page.content();
        const $$ = cheerio.load(updatedContent);

        // Try different selectors for photos
        const photoElements = $$(
          'img[src*="photo"], img[src*="places"], img[data-src*="photo"], img[data-src*="places"]'
        );

        console.log(`    Found ${photoElements.length} photos in gallery`);

        photoElements.each((i, el) => {
          // Try to get the highest quality version of the image
          let src = $$(el).attr("src");
          if (!src) {
            src = $$(el).attr("data-src");
          }

          // If found a thumbnail, try to get the full-sized image
          if (src && src.includes("=w")) {
            // Replace size parameters to get higher quality
            src = src.replace(/=w\d+-h\d+/, "=w1000-h1000");
          }

          if (src && !details.photos.includes(src)) {
            details.photos.push(src);
          }
        });
      }
    } catch (photoError) {
      console.log(
        `    Error extracting photos from gallery: ${photoError.message}`
      );
    }

    // Fallback: extract photos from the main page if we don't have enough photos yet
    if (details.photos.length < minRequiredPhotos) {
      console.log(
        `    Need more photos (have ${details.photos.length}/${minRequiredPhotos}). Extracting from main page...`
      );

      // Look for all possible image elements
      const photoElements = $(
        'img[src*="photo"], img[src*="places"], img[src*="googleapis"], img[width][height]:not([src*="gstatic"]):not([src*="google"]):not([src*="icon"])'
      );

      photoElements.each((i, el) => {
        // Stop if we already have enough photos
        if (details.photos.length >= minRequiredPhotos) {
          return false; // Break the each loop
        }

        let src = $(el).attr("src");

        // Skip very small images (likely icons)
        const width = $(el).attr("width");
        const height = $(el).attr("height");
        if (
          width &&
          height &&
          (parseInt(width) < 50 || parseInt(height) < 50)
        ) {
          return;
        }

        // Try to get the highest quality version of the image
        if (src && src.includes("=w")) {
          // Replace size parameters to get higher quality
          src = src.replace(/=w\d+-h\d+/, "=w1000-h1000");
        }

        if (src && !details.photos.includes(src)) {
          details.photos.push(src);
        }
      });
    }

    // If we still don't have enough photos, try additional strategies
    if (details.photos.length < minRequiredPhotos) {
      console.log(
        `    Still need more photos (have ${details.photos.length}/${minRequiredPhotos}). Trying additional strategies...`
      );

      // Try to find and click on different photo sections
      try {
        // Try to navigate to the photos tab directly if it exists
        const morePhotosFound = await page.evaluate(() => {
          // Look for "see all photos", "view more photos" or similar buttons
          const photoButtons = Array.from(
            document.querySelectorAll(
              'a[href*="photo"], button[aria-label*="photo"], div[aria-label*="photo"]'
            )
          );

          // Click the first one that's visible
          for (const button of photoButtons) {
            if (button.offsetParent !== null) {
              // Check if element is visible
              button.click();
              return true;
            }
          }
          return false;
        });

        if (morePhotosFound) {
          // Wait for new photos to load
          await page.waitForTimeout(2000);

          // Get updated content
          const morePhotosContent = await page.content();
          const $$$ = cheerio.load(morePhotosContent);

          // Look for images with broader selectors
          const morePhotoElements = $$$(
            'img[src*="http"]:not([src*="gstatic"]):not([src*="icon"])'
          );

          console.log(
            `    Found ${morePhotoElements.length} additional photos`
          );

          morePhotoElements.each((i, el) => {
            let src = $$$(el).attr("src");

            // Skip small images
            const width = $$$(el).attr("width");
            const height = $$$(el).attr("height");
            if (
              width &&
              height &&
              (parseInt(width) < 50 || parseInt(height) < 50)
            ) {
              return;
            }

            if (src && !details.photos.includes(src)) {
              details.photos.push(src);

              // Stop if we have enough photos
              if (details.photos.length >= minRequiredPhotos) {
                return false;
              }
            }
          });
        }
      } catch (additionalPhotoError) {
        console.log(
          `    Error extracting additional photos: ${additionalPhotoError.message}`
        );
      }
    }

    // Log the final photo count
    if (details.photos.length >= minRequiredPhotos) {
      console.log(
        `    Successfully extracted ${details.photos.length} photos (minimum ${minRequiredPhotos} required)`
      );
    } else {
      console.log(
        `    Warning: Only extracted ${details.photos.length} photos (minimum ${minRequiredPhotos} required)`
      );
    }

    // Try to extract services
    // Google Maps doesn't have a dedicated services section, so we'll try to infer from categories and description

    // Look for categories
    const categoryElements = $('button[jsaction*="pane.rating.category"]');
    if (categoryElements.length) {
      categoryElements.each((i, el) => {
        const category = $(el).text().trim();
        if (category && !details.services.includes(category)) {
          details.services.push(category);
        }
      });
    }

    // Look for services in the description
    const descriptionElement = $(
      'div[data-attrid="kc:/local:merchant_description"]'
    );
    if (descriptionElement.length) {
      const description = descriptionElement.text().trim();
      // Split description into sentences and look for service-related keywords
      const sentences = description.split(/[.!?]+/);
      for (const sentence of sentences) {
        if (/service|provide|specialize|offer/i.test(sentence)) {
          const service = sentence.trim();
          if (service && !details.services.includes(service)) {
            details.services.push(service);
          }
        }
      }
    }

    // If no services found, try to extract from reviews or Q&A
    if (details.services.length === 0) {
      const reviewElements = $("div[data-review-id] div.review-content");
      if (reviewElements.length) {
        for (let i = 0; i < Math.min(reviewElements.length, 5); i++) {
          const reviewText = $(reviewElements[i]).text().trim();
          const sentences = reviewText.split(/[.!?]+/);
          for (const sentence of sentences) {
            if (
              /service|provide|specialize|offer|work|repair|install/i.test(
                sentence
              )
            ) {
              const service = sentence.trim();
              if (
                service &&
                !details.services.includes(service) &&
                service.length < 100
              ) {
                details.services.push(service);
              }
            }
          }
        }
      }
    }

    // Extract reviews
    console.log("    Extracting reviews...");

    // Try to find the reviews section
    // First, try to click on the reviews tab if it exists
    try {
      const reviewsTabExists = await page.evaluate(() => {
        const reviewsTab = document.querySelector(
          'button[aria-label*="review"], button[data-tab-index*="reviews"], button[jsaction*="reviews"]'
        );
        if (reviewsTab) {
          reviewsTab.click();
          return true;
        }
        return false;
      });

      if (reviewsTabExists) {
        // Wait for reviews to load
        await page.waitForSelector(
          'div[data-review-id], div[jsaction*="reviewEntry"]',
          { timeout: 5000 }
        );

        // Scroll down to load more reviews
        await autoScroll(page);

        // Get updated content after clicking and scrolling
        const updatedContent = await page.content();
        const $$ = cheerio.load(updatedContent);

        // Try different selectors for reviews
        const reviewContainers = $$(
          'div[data-review-id], div[jsaction*="reviewEntry"]'
        );

        console.log(`    Found ${reviewContainers.length} reviews`);

        reviewContainers.each((i, el) => {
          const reviewContainer = $$(el);

          // Extract review text
          const reviewTextElement = reviewContainer.find(
            'span[jsan*="text"], div.review-full-text'
          );
          let reviewText = "";

          if (reviewTextElement.length) {
            reviewText = reviewTextElement.text().trim();
          } else {
            // Try alternative selectors
            const possibleTextElements = reviewContainer.find("span");
            possibleTextElements.each((j, textEl) => {
              const text = $$(textEl).text().trim();
              if (text.length > 20) {
                // Likely a review text if it's long enough
                reviewText = text;
                return false; // Break the loop
              }
            });
          }

          // Extract rating
          const ratingElement = reviewContainer.find(
            'span[aria-label*="star"], span[aria-label*="Star"]'
          );
          let rating = "";

          if (ratingElement.length) {
            const ratingText = ratingElement.attr("aria-label");
            const ratingMatch = ratingText
              ? ratingText.match(/(\d+(\.\d+)?)\s*star/)
              : null;
            rating = ratingMatch ? ratingMatch[1] : "";
          }

          // Extract author name
          const authorElement = reviewContainer.find(
            'div[jsan*="author"], a[href*="contrib"]'
          );
          let author = "";

          if (authorElement.length) {
            author = authorElement.text().trim();
          }

          // Extract date
          const dateElement = reviewContainer.find(
            'span[jsan*="date"], span.review-date'
          );
          let date = "";

          if (dateElement.length) {
            date = dateElement.text().trim();
          }

          // Create review object
          if (reviewText) {
            const review = {
              text: reviewText,
              rating: rating,
              author: author,
              date: date,
            };

            details.reviews.push(review);
          }
        });
      } else {
        console.log("    No reviews tab found");
      }
    } catch (error) {
      console.log(`    Error extracting reviews: ${error.message}`);

      // Try an alternative approach if clicking the tab failed
      try {
        // Look for reviews directly in the current page
        const reviewElements = $(
          'div[data-review-id], div[jsaction*="reviewEntry"]'
        );

        console.log(`    Found ${reviewElements.length} reviews directly`);

        reviewElements.each((i, el) => {
          const reviewElement = $(el);

          // Extract review text
          const reviewTextElement = reviewElement.find(
            'span[jsan*="text"], div.review-full-text'
          );
          let reviewText = "";

          if (reviewTextElement.length) {
            reviewText = reviewTextElement.text().trim();
          } else {
            // Try alternative selectors
            const possibleTextElements = reviewElement.find("span");
            possibleTextElements.each((j, textEl) => {
              const text = $(textEl).text().trim();
              if (text.length > 20) {
                // Likely a review text if it's long enough
                reviewText = text;
                return false; // Break the loop
              }
            });
          }

          // Extract rating
          const ratingElement = reviewElement.find(
            'span[aria-label*="star"], span[aria-label*="Star"]'
          );
          let rating = "";

          if (ratingElement.length) {
            const ratingText = ratingElement.attr("aria-label");
            const ratingMatch = ratingText
              ? ratingText.match(/(\d+(\.\d+)?)\s*star/)
              : null;
            rating = ratingMatch ? ratingMatch[1] : "";
          }

          // Extract author name
          const authorElement = reviewElement.find(
            'div[jsan*="author"], a[href*="contrib"]'
          );
          let author = "";

          if (authorElement.length) {
            author = authorElement.text().trim();
          }

          // Extract date
          const dateElement = reviewElement.find(
            'span[jsan*="date"], span.review-date'
          );
          let date = "";

          if (dateElement.length) {
            date = dateElement.text().trim();
          }

          // Create review object
          if (reviewText) {
            const review = {
              text: reviewText,
              rating: rating,
              author: author,
              date: date,
            };

            details.reviews.push(review);
          }
        });
      } catch (reviewError) {
        console.log(
          `    Error extracting reviews directly: ${reviewError.message}`
        );
      }
    }
  } catch (error) {
    console.error(`    Error scraping details from ${businessUrl}:`, error);
    throw error; // Rethrow to trigger retry in the parent function
  } finally {
    await page.close();
  }

  return details;
}

// Keep the original function for backward compatibility
async function scrapeBusinessDetails(browser, businessUrl) {
  console.warn(
    "Warning: scrapeBusinessDetails is deprecated, use scrapeGoogleMapsBusinessDetails instead"
  );
  return scrapeGoogleMapsBusinessDetails(browser, businessUrl, "Unknown");
}

// Function to validate that business data is real and not placeholder
function isValidBusinessData(business) {
  // Must have a name
  if (!business.name || business.name.trim() === "") {
    return false;
  }

  // Skip businesses with placeholder/sample names
  const placeholderNames = [
    "sample business",
    "test business",
    "placeholder",
    "example business",
    "demo business",
  ];

  const lowerName = business.name.toLowerCase();
  if (placeholderNames.some((placeholder) => lowerName.includes(placeholder))) {
    return false;
  }

  // Must have at least one piece of contact information
  const hasContactInfo =
    business.address || business.phone || business.website || business.email;
  if (!hasContactInfo) {
    return false;
  }

  // Skip businesses with obviously fake data
  if (
    business.phone &&
    (business.phone.includes("555-000") ||
      business.phone.includes("123-456") ||
      business.phone === "************")
  ) {
    return false;
  }

  if (
    business.email &&
    (business.email.includes("@scraped.com") ||
      business.email.includes("@sample.com") ||
      business.email.includes("@test.com"))
  ) {
    return false;
  }

  if (
    business.website &&
    (business.website.includes("sample-business.com") ||
      business.website.includes("placeholder.com") ||
      business.website.includes("example.com"))
  ) {
    return false;
  }

  return true;
}

// Add a simple command-line interface
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  const options = {
    locations: locations.slice(), // Default to all locations
    categories: categories.slice(), // Default to all categories
    maxBusinessesPerCategory: 10,
    resume: true,
    help: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === "--locations" || arg === "-l") {
      const locationArg = args[++i];
      if (locationArg) {
        options.locations = locationArg.split(",").map((loc) => loc.trim());
      }
    } else if (arg === "--categories" || arg === "-c") {
      const categoryArg = args[++i];
      if (categoryArg) {
        options.categories = categoryArg.split(",").map((cat) => cat.trim());
      }
    } else if (arg === "--max" || arg === "-m") {
      const maxArg = args[++i];
      if (maxArg && !isNaN(parseInt(maxArg))) {
        options.maxBusinessesPerCategory = parseInt(maxArg);
      }
    } else if (arg === "--no-resume" || arg === "-n") {
      options.resume = false;
    } else if (arg === "--help" || arg === "-h") {
      options.help = true;
    }
  }

  return options;
}

function displayHelp() {
  console.log(`
Business Scraper - A tool to scrape business data from Google Maps

Usage:
  node index.js [options]

Options:
  --locations, -l    Comma-separated list of locations to scrape (default: Hayward, San Leandro, Oakland)
  --categories, -c   Comma-separated list of categories to scrape (default: all categories)
  --max, -m          Maximum number of businesses to scrape per category (default: 10)
  --no-resume, -n    Don't resume from previous scraping session (default: resume if possible)
  --help, -h         Display this help message

Examples:
  node index.js --locations "Oakland, CA" --categories "Plumbing,Electrical" --max 5
  node index.js -l "Hayward, CA" -c "HVAC" -n
  `);
}

// Main function
async function main() {
  const options = parseCommandLineArgs();

  if (options.help) {
    displayHelp();
    return;
  }

  // If not resuming, delete the temp file
  if (!options.resume && fs.existsSync(config.tempFile)) {
    fs.unlinkSync(config.tempFile);
    console.log("Deleted previous temporary file to start fresh.");
  }

  // Update config with command-line options
  config.maxBusinessesPerCategory = options.maxBusinessesPerCategory;

  console.log("Starting business scraper with the following options:");
  console.log(`Locations: ${options.locations.join(", ")}`);
  console.log(`Categories: ${options.categories.join(", ")}`);
  console.log(
    `Max businesses per category: ${options.maxBusinessesPerCategory}`
  );
  console.log(`Resume previous session: ${options.resume ? "Yes" : "No"}`);
  console.log("");

  // Override the global locations and categories with the ones from command-line
  const originalLocations = locations.slice();
  const originalCategories = categories.slice();

  // Replace the global arrays (used by scrapeBusinessData)
  while (locations.length > 0) locations.pop();
  while (categories.length > 0) categories.pop();

  options.locations.forEach((loc) => locations.push(loc));
  options.categories.forEach((cat) => categories.push(cat));

  try {
    await scrapeBusinessData();
    console.log("Scraping process completed successfully");
  } catch (err) {
    console.error("Error in main process:", err);
  } finally {
    // Restore the original arrays
    while (locations.length > 0) locations.pop();
    while (categories.length > 0) categories.pop();

    originalLocations.forEach((loc) => locations.push(loc));
    originalCategories.forEach((cat) => categories.push(cat));
  }
}

// Run the main function
main();
